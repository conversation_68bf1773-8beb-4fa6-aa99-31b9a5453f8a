/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Badges Command Handler
 *
 * Handles user badge display preferences configuration.
 */

import { SlashCommandBuilder } from 'discord.js';
import { inject, injectable } from 'inversify';
import type { UpdateUserUseCase } from '../../../application/use-cases/users/UpdateUserUseCase.js';
import type { Context } from '../../../shared/context/Context.js';
import { TYPES } from '../../../shared/types/TYPES.js';
import { BaseCommandHandler, CommandCategory, type CommandResult } from '../BaseCommandHandler.js';

/**
 * Badges Command Handler
 *
 * Allows users to configure their badge display preferences.
 */
@injectable()
export default class BadgesCommandHandler extends BaseCommandHandler {
  readonly metadata = {
    name: 'badges',
    description: '🏅 Configure your badge display preferences',
    category: CommandCategory.CONFIG,
    staffOnly: false,
    ownerOnly: false,
    guildOnly: false,
    cooldown: 3000,
  };

  constructor(
    @inject(TYPES.UpdateUserUseCase)
    private readonly updateUserUseCase: UpdateUserUseCase,
  ) {
    super();
  }

  buildCommand() {
    return new SlashCommandBuilder()
      .setName(this.metadata.name)
      .setDescription(this.metadata.description)
      .addBooleanOption((option) =>
        option
          .setName('show')
          .setDescription('Whether to show or hide your badges in messages')
          .setRequired(true),
      );
  }

  async execute(ctx: Context): Promise<CommandResult> {
    try {
      const showBadges = ctx.options.getBoolean('show', true) ?? false;

      // Update user preferences using the use case
      const result = await this.updateUserUseCase.execute({
        userId: ctx.user.id,
        name: ctx.user.username,
        image: ctx.user.displayAvatarURL(),
        showBadges,
      });

      if (result.updated) {
        const embed = this.createSuccessEmbed(
          'Badge Preferences Updated',
          showBadges
            ? '✅ Your badges will now be shown in messages'
            : '✅ Your badges will now be hidden in messages',
        );

        return { success: true, embed, ephemeral: true };
      } else {
        const embed = this.createInfoEmbed(
          'No Changes Made',
          'Your badge preferences are already set to this value.',
        );

        return {
          success: true,
          embed,
          ephemeral: true,
        };
      }
    } catch (error) {
      const embed = this.createErrorEmbed(
        'Failed to Update Preferences',
        'An error occurred while updating your badge preferences. Please try again later.',
      );

      return {
        success: false,
        embed,
        ephemeral: true,
      };
    }
  }
}
