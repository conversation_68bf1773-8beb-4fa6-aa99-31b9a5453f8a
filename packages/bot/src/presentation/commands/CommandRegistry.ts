/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { injectable, multiInject } from 'inversify';
import { ChatInputCommandInteraction, Collection, SlashCommandBuilder, SlashCommandOptionsOnlyBuilder, SlashCommandSubcommandsOnlyBuilder, Message } from 'discord.js';
import { TYPES } from '../../shared/types/TYPES.js';
import { BaseCommandHandler, CommandResult, FlexibleCommandResponse, EnhancedCommandResult } from './BaseCommandHandler.js';
import { InteractionContext } from '../../shared/context/index.js';
import { PrefixContext } from '../../shared/context/PrefixContext.js';
import { createUnifiedContext } from '../../shared/context/UnifiedContext.js';
import { Logger } from '../../shared/utils/Logger.js';

/**
 * Command Registry
 *
 * Manages all Discord slash commands and their execution.
 * Implements the Registry pattern with dependency injection.
 */
@injectable()
export class CommandRegistry {
  private commands = new Collection<string, BaseCommandHandler>();
  private cooldowns = new Collection<string, Collection<string, number>>();

  constructor(
    @multiInject(TYPES.PresentationCommandHandler)
    private commandHandlers: BaseCommandHandler[]
  ) {
    this.registerCommands();
  }

  /**
   * Register all command handlers
   */
  private registerCommands(): void {
    for (const handler of this.commandHandlers) {
      this.commands.set(handler.metadata.name, handler);
      Logger.debug(`Registered command: ${handler.metadata.name}`);
    }
  }

  /**
   * Get all command data for Discord API registration
   */
  getCommandData(): (SlashCommandBuilder | SlashCommandOptionsOnlyBuilder | SlashCommandSubcommandsOnlyBuilder)[] {
    return this.commands.map(command => command.buildCommand());
  }

  /**
   * Execute a slash command
   */
  async executeCommand(interaction: ChatInputCommandInteraction): Promise<void> {
    const commandName = interaction.commandName;
    const command = this.commands.get(commandName);

    if (!command) {
      await this.handleUnknownCommand(interaction);
      return;
    }

    try {
      // Create Context from interaction
      const context = new InteractionContext(interaction);

      // Check cooldown
      if (!this.checkCooldown(interaction, command)) {
        await this.handleCooldown(interaction, command);
        return;
      }

      // Check permissions
      if (!await command.checkPermissions(context)) {
        await this.handlePermissionDenied(interaction);
        return;
      }

      // Execute command
      const result = await command.execute(context);

      // Set cooldown
      this.setCooldown(interaction, command);

      // Handle response based on result type
      await this.handleFlexibleCommandResult(interaction, result);

    } catch (error) {
      console.error(`Error executing command ${commandName}:`, error);
      await this.handleCommandError(interaction, error);
    }
  }

  /**
   * Execute a prefix command
   */
  async executePrefixCommand(message: Message, commandName: string, args: string[]): Promise<void> {
    const command = this.commands.get(commandName);

    if (!command) {
      await this.handleUnknownPrefixCommand(message, commandName);
      return;
    }

    try {
      // Create Context from message
      const prefixContext = new PrefixContext(message, args);
      const context = createUnifiedContext(prefixContext);

      // Check cooldown
      if (!this.checkPrefixCooldown(message, command)) {
        await this.handlePrefixCooldown(message, command);
        return;
      }

      // Check permissions
      if (!await command.checkPermissions(context as any)) {
        await this.handlePrefixPermissionDenied(message);
        return;
      }

      // Execute command
      const result = await command.execute(context as any);

      // Set cooldown
      this.setPrefixCooldown(message, command);

      // Handle response based on result type
      await this.handleFlexiblePrefixCommandResult(message, result);

    } catch (error) {
      console.error(`Error executing prefix command ${commandName}:`, error);
      await this.handlePrefixCommandError(message, error);
    }
  }

  /**
   * Check if user is on cooldown
   */
  private checkCooldown(interaction: ChatInputCommandInteraction, command: BaseCommandHandler): boolean {
    const { cooldown } = command.metadata;
    if (!cooldown) return true;

    const userId = interaction.user.id;
    const commandName = command.metadata.name;

    if (!this.cooldowns.has(commandName)) {
      this.cooldowns.set(commandName, new Collection());
    }

    const now = Date.now();
    const timestamps = this.cooldowns.get(commandName)!;
    const cooldownAmount = cooldown * 1000;

    if (timestamps.has(userId)) {
      const expirationTime = timestamps.get(userId)! + cooldownAmount;
      if (now < expirationTime) {
        return false;
      }
    }

    return true;
  }

  /**
   * Set cooldown for user
   */
  private setCooldown(interaction: ChatInputCommandInteraction, command: BaseCommandHandler): void {
    const { cooldown } = command.metadata;
    if (!cooldown) return;

    const userId = interaction.user.id;
    const commandName = command.metadata.name;
    const now = Date.now();

    if (!this.cooldowns.has(commandName)) {
      this.cooldowns.set(commandName, new Collection());
    }

    const timestamps = this.cooldowns.get(commandName)!;
    timestamps.set(userId, now);

    // Clean up expired cooldowns
    setTimeout(() => timestamps.delete(userId), cooldown * 1000);
  }

  /**
   * Handle flexible command result - supports void, CommandResult, or EnhancedCommandResult
   */
  private async handleFlexibleCommandResult(
    interaction: ChatInputCommandInteraction,
    result: FlexibleCommandResponse
  ): Promise<void> {
    // If result is void, command handled response directly - do nothing
    if (result === undefined || result === null) {
      return;
    }

    // Handle structured responses
    await this.handleStructuredCommandResult(interaction, result);
  }

  /**
   * Handle structured command result (CommandResult or EnhancedCommandResult)
   */
  private async handleStructuredCommandResult(
    interaction: ChatInputCommandInteraction,
    result: CommandResult | EnhancedCommandResult
  ): Promise<void> {
    // Check if interaction was already handled
    if (interaction.replied || interaction.deferred) {
      // If already replied/deferred, we can only edit or follow up
      await this.handleAlreadyRepliedInteraction(interaction, result);
      return;
    }

    // Build response object
    const response: any = {
      content: result.message,
      embeds: result.embed ? [result.embed] : [],
      ephemeral: result.ephemeral || false
    };

    // Add enhanced features if available
    if ('components' in result && result.components) {
      response.components = result.components;
    }
    if ('files' in result && result.files) {
      response.files = result.files;
    }
    if ('allowedMentions' in result && result.allowedMentions) {
      response.allowedMentions = result.allowedMentions;
    }
    if ('flags' in result && result.flags) {
      response.flags = result.flags;
    }

    // Send initial response
    await interaction.reply(response);

    // Send follow-up if specified
    if (result.followUp) {
      await interaction.followUp({
        content: result.followUp,
        ephemeral: true
      });
    }
  }

  /**
   * Handle response when interaction was already replied to or deferred
   */
  private async handleAlreadyRepliedInteraction(
    interaction: ChatInputCommandInteraction,
    result: CommandResult | EnhancedCommandResult
  ): Promise<void> {
    const response: any = {
      content: result.message,
      embeds: result.embed ? [result.embed] : []
    };

    // Add enhanced features if available
    if ('components' in result && result.components) {
      response.components = result.components;
    }
    if ('files' in result && result.files) {
      response.files = result.files;
    }

    try {
      await interaction.editReply(response);
    } catch (error) {
      // If edit fails, try follow-up
      console.warn('Failed to edit reply, attempting follow-up:', error);
      await interaction.followUp({
        ...response,
        ephemeral: result.ephemeral || false
      });
    }

    // Send additional follow-up if specified
    if (result.followUp) {
      await interaction.followUp({
        content: result.followUp,
        ephemeral: true
      });
    }
  }

  /**
   * Legacy method for backward compatibility
   * @deprecated Use handleFlexibleCommandResult instead
   */
  private async handleCommandResult(
    interaction: ChatInputCommandInteraction,
    result: CommandResult
  ): Promise<void> {
    await this.handleStructuredCommandResult(interaction, result);
  }

  /**
   * Handle unknown command
   */
  private async handleUnknownCommand(interaction: ChatInputCommandInteraction): Promise<void> {
    await interaction.reply({
      content: '❌ Unknown command.',
      ephemeral: true
    });
  }

  /**
   * Handle cooldown
   */
  private async handleCooldown(
    interaction: ChatInputCommandInteraction,
    command: BaseCommandHandler
  ): Promise<void> {
    const { cooldown } = command.metadata;
    const userId = interaction.user.id;
    const commandName = command.metadata.name;

    const timestamps = this.cooldowns.get(commandName)!;
    const cooldownAmount = cooldown! * 1000;
    const expirationTime = timestamps.get(userId)! + cooldownAmount;
    const timeLeft = (expirationTime - Date.now()) / 1000;

    await interaction.reply({
      content: `⏰ Please wait ${timeLeft.toFixed(1)} more seconds before using \`${commandName}\` again.`,
      ephemeral: true
    });
  }

  /**
   * Handle permission denied
   */
  private async handlePermissionDenied(interaction: ChatInputCommandInteraction): Promise<void> {
    await interaction.reply({
      content: '🔒 You do not have permission to use this command.',
      ephemeral: true
    });
  }

  /**
   * Handle command error
   */
  private async handleCommandError(
    interaction: ChatInputCommandInteraction,
    error: unknown
  ): Promise<void> {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    const response = {
      content: `❌ An error occurred while executing this command: ${errorMessage}`,
      ephemeral: true
    };

    try {
      if (interaction.replied || interaction.deferred) {
        await interaction.editReply(response);
      } else {
        await interaction.reply(response);
      }
    } catch (replyError) {
      console.error('Failed to send error message:', replyError);
    }
  }

  /**
   * Get command by name
   */
  getCommand(name: string): BaseCommandHandler | undefined {
    return this.commands.get(name);
  }

  /**
   * Get all commands
   */
  getAllCommands(): BaseCommandHandler[] {
    return Array.from(this.commands.values());
  }

  /**
   * Get commands by category
   */
  getCommandsByCategory(category: string): BaseCommandHandler[] {
    return this.getAllCommands().filter(command => command.metadata.category === category);
  }

  /**
   * Handle flexible command result for prefix commands
   */
  private async handleFlexiblePrefixCommandResult(
    message: Message,
    result: FlexibleCommandResponse
  ): Promise<void> {
    // If result is void, command handled response directly - do nothing
    if (result === undefined || result === null) {
      return;
    }

    // Handle structured responses for prefix commands
    await this.handleStructuredPrefixCommandResult(message, result);
  }

  /**
   * Handle structured command result for prefix commands
   */
  private async handleStructuredPrefixCommandResult(
    message: Message,
    result: CommandResult | EnhancedCommandResult
  ): Promise<void> {
    // Build response object
    const response: any = {};

    if (result.message) {
      response.content = result.message;
    }

    if (result.embed) {
      response.embeds = [result.embed];
    }

    // Add enhanced features if available
    if ('components' in result && result.components) {
      response.components = result.components;
    }
    if ('files' in result && result.files) {
      response.files = result.files;
    }
    if ('allowedMentions' in result && result.allowedMentions) {
      response.allowedMentions = result.allowedMentions;
    }
    // Note: ephemeral and flags are not supported for prefix commands

    // Send response
    await message.reply(response);

    // Send follow-up if specified
    if (result.followUp) {
      const channel = message.channel;
      if (channel && 'send' in channel) {
        await channel.send(result.followUp);
      }
    }
  }

  /**
   * Check cooldown for prefix commands
   */
  private checkPrefixCooldown(message: Message, command: BaseCommandHandler): boolean {
    return this.checkCooldown(message as any, command);
  }

  /**
   * Set cooldown for prefix commands
   */
  private setPrefixCooldown(message: Message, command: BaseCommandHandler): void {
    this.setCooldown(message as any, command);
  }

  /**
   * Handle unknown prefix command
   */
  private async handleUnknownPrefixCommand(message: Message, commandName: string): Promise<void> {
    // Could implement command suggestions here
    console.log(`Unknown prefix command: ${commandName}`);
  }

  /**
   * Handle prefix command cooldown
   */
  private async handlePrefixCooldown(message: Message, command: BaseCommandHandler): Promise<void> {
    const cooldownTime = command.metadata.cooldown || 3;
    await message.reply(`⏰ Please wait ${cooldownTime} seconds before using this command again.`);
  }

  /**
   * Handle prefix permission denied
   */
  private async handlePrefixPermissionDenied(message: Message): Promise<void> {
    await message.reply('❌ You do not have permission to use this command.');
  }

  /**
   * Handle prefix command error
   */
  private async handlePrefixCommandError(message: Message, error: unknown): Promise<void> {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    await message.reply(`❌ An error occurred while executing this command: ${errorMessage}`);
  }
}
