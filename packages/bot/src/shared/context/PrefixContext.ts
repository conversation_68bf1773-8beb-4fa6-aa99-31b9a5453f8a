/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Prefix Context for Message-based Commands
 *
 * Provides a unified context interface for prefix commands that matches
 * the slash command context interface, enabling the same flexible response patterns.
 */

import {
  Message,
  MessagePayload,
  MessageReplyOptions,
  InteractionReplyOptions,
  MessageEditOptions,
  JSONEncodable,
  APIModalInteractionResponseCallbackData,
  ModalComponentData,
  User,
  Channel,
  Role,
  Attachment,
  TextBasedChannel
} from 'discord.js';
import { Context, ContextT } from './Context.js';
import { ContextOptions } from './ContextOptions.js';

/**
 * Context class for prefix command messages
 */
export class PrefixContext extends Context<{
  interaction: Message;
  responseType: Message;
}> {
  private _args: string[];
  private _replied = false;
  private _deferred = false;

  constructor(message: Message, args: string[] = []) {
    super(message);
    this._args = args;
  }

  /**
   * Get command arguments
   */
  get args(): string[] {
    return this._args;
  }

  /**
   * Whether the interaction has been deferred (not applicable for prefix commands)
   */
  get deferred(): boolean {
    return this._deferred;
  }

  /**
   * Whether the interaction has been replied to
   */
  get replied(): boolean {
    return this._replied;
  }

  /**
   * Defer reply (not applicable for prefix commands, but provided for compatibility)
   */
  async deferReply(_opts?: { flags?: ['Ephemeral'] }): Promise<Message | null> {
    this._deferred = true;
    // For prefix commands, we can't actually defer, so this is a no-op
    return null;
  }

  /**
   * Reply to the message
   */
  async reply(
    data: string | MessagePayload | MessageReplyOptions | InteractionReplyOptions | MessageEditOptions
  ): Promise<Message> {
    this._replied = true;

    // Convert InteractionReplyOptions to MessageReplyOptions if needed
    if (typeof data === 'object' && data !== null && 'ephemeral' in data) {
      const { ephemeral, ...messageData } = data as any;
      // Prefix commands can't be ephemeral, so we ignore that flag
      return await this.interaction.reply(messageData);
    }

    return await this.interaction.reply(data as string | MessageReplyOptions);
  }

  /**
   * Edit the reply (for prefix commands, this sends a new message)
   */
  async editReply(
    data: string | MessagePayload | MessageEditOptions
  ): Promise<Message> {
    // For prefix commands, we can't edit replies, so we send a new message
    const channel = this.interaction.channel;
    if (!channel || !('send' in channel)) {
      throw new Error('Cannot send message to this channel type');
    }
    return await channel.send(data as any);
  }

  /**
   * Delete the reply
   */
  async deleteReply(): Promise<void> {
    // For prefix commands, we can't delete the original reply easily
    // This would need to track the reply message
    throw new Error('deleteReply not implemented for prefix commands');
  }

  /**
   * Follow up with another message
   */
  async followUp(
    data: string | MessagePayload | MessageReplyOptions
  ): Promise<Message> {
    const channel = this.interaction.channel;
    if (!channel.isSendable()) {
      throw new Error('Cannot send message to this channel type');
    }
    return await channel.send(data);
  }

  /**
   * Show a modal (not supported for prefix commands)
   */
  async showModal(
    _data: JSONEncodable<APIModalInteractionResponseCallbackData> | ModalComponentData | APIModalInteractionResponseCallbackData
  ): Promise<void> {
    throw new Error('Modals are not supported for prefix commands');
  }

  /**
   * Get a string argument by index
   */
  getString(index: number): string | null {
    return this._args[index] || null;
  }

  /**
   * Get an integer argument by index
   */
  getInteger(index: number): number | null {
    const value = this._args[index];
    if (!value) return null;

    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? null : parsed;
  }

  /**
   * Get a number argument by index
   */
  getNumber(index: number): number | null {
    const value = this._args[index];
    if (!value) return null;

    const parsed = parseFloat(value);
    return isNaN(parsed) ? null : parsed;
  }

  /**
   * Get a boolean argument by index
   */
  getBoolean(index: number): boolean | null {
    const value = this._args[index];
    if (!value) return null;

    const lower = value.toLowerCase();
    if (lower === 'true' || lower === 'yes' || lower === '1') return true;
    if (lower === 'false' || lower === 'no' || lower === '0') return false;

    return null;
  }

  /**
   * Get a user argument by index (mentions or IDs)
   */
  async getUser(index: number): Promise<User | null> {
    const value = this._args[index];
    if (!value) return null;

    // Try to parse mention
    const mentionMatch = value.match(/^<@!?(\d+)>$/);
    if (mentionMatch) {
      try {
        return await this.interaction.client.users.fetch(mentionMatch[1]);
      } catch {
        return null;
      }
    }

    // Try to parse as user ID
    if (/^\d{17,19}$/.test(value)) {
      try {
        return await this.interaction.client.users.fetch(value);
      } catch {
        return null;
      }
    }

    return null;
  }

  /**
   * Get a channel argument by index (mentions or IDs)
   */
  getChannel(index: number): Channel | null {
    const value = this._args[index];
    if (!value) return null;

    // Try to parse mention
    const mentionMatch = value.match(/^<#(\d+)>$/);
    if (mentionMatch) {
      return this.interaction.client.channels.cache.get(mentionMatch[1]) || null;
    }

    // Try to parse as channel ID
    if (/^\d{17,19}$/.test(value)) {
      return this.interaction.client.channels.cache.get(value) || null;
    }

    return null;
  }

  /**
   * Get a role argument by index (mentions or IDs)
   */
  getRole(index: number): Role | null {
    const value = this._args[index];
    if (!value || !this.interaction.guild) return null;

    // Try to parse mention
    const mentionMatch = value.match(/^<@&(\d+)>$/);
    if (mentionMatch) {
      return this.interaction.guild.roles.cache.get(mentionMatch[1]) || null;
    }

    // Try to parse as role ID
    if (/^\d{17,19}$/.test(value)) {
      return this.interaction.guild.roles.cache.get(value) || null;
    }

    return null;
  }

  /**
   * Get subcommand name (first argument)
   */
  getSubcommand(): string | null {
    return this._args[0] || null;
  }

  /**
   * Get remaining arguments as a single string
   */
  getRemainingArgs(startIndex: number = 0): string {
    return this._args.slice(startIndex).join(' ');
  }
}
