/**
 * Copyright (c) 2024 InterChat
 *
 * This file is part of InterChat, licensed under the AGPL-3.0 license.
 * See the LICENSE file in the root directory for license information.
 */

/**
 * Get User Use Case
 *
 * Retrieves user information from the system.
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../../../shared/types/TYPES.js';
import type { IUserRepository } from '../../../domain/repositories/UserRepositories.js';
import { ApplicationError } from '../../../shared/errors/DomainError.js';
import type { HubActivityLevel } from '../../../../../../build/generated/prisma/client/index.js';

export interface GetUserQuery {
  readonly userId: string;
  readonly includeActivity?: boolean;
  readonly includePreferences?: boolean;
  readonly includeBanInfo?: boolean;
}

export interface GetUserResult {
  readonly userId: string;
  readonly name: string | null;
  readonly image: string | null;
  readonly email: string | null;
  readonly isStaff: boolean;
  readonly createdAt: Date;
  readonly updatedAt: Date;
  readonly activity?: {
    messageCount: number;
    lastMessageAt: Date;
    voteCount: number;
    lastVoted: Date | null;
    reputation: number;
    hubJoinCount: number;
    lastHubJoinAt: Date | null;
    hubEngagementScore: number;
  };
  readonly preferences?: {
    showBadges: boolean;
    mentionOnReply: boolean;
    locale: string | null;
    preferredLanguages: string[];
    activityLevel: HubActivityLevel | null;
    showNsfwHubs: boolean;
  };
  readonly banInfo?: {
    reason: string | null;
    bannedAt: Date | null;
    isActive: boolean;
  };
}

/**
 * Use Case: Get User
 *
 * Retrieves user information with optional detailed data.
 */
@injectable()
export class GetUserUseCase {
  constructor(
    @inject(TYPES.UserRepository)
    private readonly userRepository: IUserRepository
  ) {}

  async execute(query: GetUserQuery): Promise<GetUserResult | null> {
    try {
      // Validate input
      this.validateQuery(query);

      // Find user
      const user = await this.userRepository.findById(query.userId);
      if (!user) {
        return null;
      }

      // Build result with all properties at once
      const result: GetUserResult = {
        userId: user.id,
        name: user.name,
        image: user.image,
        email: user.email,
        isStaff: user.isStaff,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        // Add optional data conditionally
        ...(query.includeActivity && { activity: user.activity }),
        ...(query.includePreferences && { preferences: user.preferences }),
        ...(query.includeBanInfo && { banInfo: user.banInfo }),
      };

      return result;
    } catch (error) {
      throw new ApplicationError(
        'Failed to get user',
        'GET_USER_FAILED',
        { originalError: error }
      );
    }
  }

  private validateQuery(query: GetUserQuery): void {
    if (!query.userId || query.userId.trim().length === 0) {
      throw new ApplicationError(
        'User ID is required',
        'INVALID_USER_ID'
      );
    }

    if (query.userId.length > 20) {
      throw new ApplicationError(
        'User ID is too long',
        'INVALID_USER_ID'
      );
    }
  }
}
